<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

use tool_courseprereq\tool_courseprereq_persistentmanager;
use tool_courseprereq\tool_courseprereq_utils;


defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/admin/tool/courseprereq/classes/tool_courseprereq_utils.php');

/**
 * Callback implementations for Course Prerequisite Manager
 *
 * @package    tool_courseprereq
 * @copyright  2025 YOUR NAME <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

/**
 * 
 */
function tool_courseprereq_extend_navigation_course(navigation_node $navigation, stdClass $course, context $context)
{
    global $PAGE, $USER;

    if (
        !tool_courseprereq_utils::is_enabled()
        || !$PAGE->has_set_url()
        || !$PAGE->url->compare(new moodle_url('/enrol/index.php'), URL_MATCH_BASE)
        || !tool_courseprereq_utils::course_has_prerequisites($course->id)
    ) {
        return;
    }

    if (tool_courseprereq_utils::has_uncompleted_prereq_courses($course->id, $USER->id)) {

        redirect(
            new moodle_url(
                '/admin/tool/courseprereq/prerequisitecourses.php',
                [
                    'courseid' => $course->id
                ]
            )
        );
    }
}
