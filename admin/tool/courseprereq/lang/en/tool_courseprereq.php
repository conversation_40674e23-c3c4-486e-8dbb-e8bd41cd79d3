<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * English language pack for Course Prerequisite Manager
 *
 * @package    tool_courseprereq
 * @category   string
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$string['pluginname'] = 'Course Prerequisite Manager';
$string['privacy:metadata'] = 'The Course Prerequisite Manager plugin doesn\'t store any personal data.';
$string['error_course_self_prerequisite'] = 'A course cannot be a prerequisite of itself';
$string['courseprereq:manage'] = 'Manage course prerequisites';

//events
$string['event:tool_courseprereq_added'] = 'Course prerequisite added';
$string['event:tool_courseprereq_removed'] = 'Course prerequisite removed';

//task
$string['task:updatetoolcourseprereqtable'] = 'Update tool_courseprereq table';

$string['settings'] = 'Course Prerequisite Manager Settings';

// Plugin settings
$string['enableplugin'] = 'Enable Plugin';
$string['enableplugin_help'] = 'Check this option to enable prerequisite control system-wide.';

$string['blockmessage'] = 'Block Message';
$string['blockmessage_help'] = 'Custom message displayed when a user tries to access a course without completing the prerequisites.';
$string['blockmessage_default'] = 'You need to complete the prerequisite courses before accessing this course.';

// Display mode
$string['displaymode'] = 'Prerequisites List Display Mode';
$string['displaymode_help'] = 'Choose how the prerequisites list will be displayed to users.';
$string['displaymode_all'] = 'All configured courses';
$string['displaymode_pending'] = 'Only courses not yet completed by the user';

// Course filters
$string['course_filters'] = 'Course Filters in Selector';

$string['filterhidden'] = 'Show Hidden Courses';
$string['filterhidden_help'] = 'Check to include hidden courses in the prerequisites selector.';

$string['filterfuture'] = 'Show Future Courses';
$string['filterfuture_help'] = 'Check to include courses with future start date in the prerequisites selector.';

$string['filterexpired'] = 'Show Expired Courses';
$string['filterexpired_help'] = 'Check to include courses with past end date in the prerequisites selector.';

// Form buttons
$string['save_settings'] = 'Save Settings';
$string['settings_saved'] = 'Settings saved successfully!';
$string['settings_error'] = 'Error saving settings. Please try again.';

$string['prereq_field_not_found'] = '"Prerequisite Courses" field not found. Please click below to create it.';
$string['create_field'] = 'Create Field "Prerequisite Courses"';
$string['prereq_field_created'] = '"Prerequisite Courses" field created successfully!';
$string['prereq_field_not_created'] = 'Error creating "Prerequisite Courses" field. Please try again.';
$string['prereq_field_exists'] = 'Prerequisites field already exists';

// Prerequisites page
$string['prerequisitecourses'] = 'Prerequisite Courses';
$string['prerequisitecourses_title'] = 'Required Prerequisites';
$string['course_completed'] = 'Completed';
$string['course_not_completed'] = 'Not Completed';
$string['back_to_course'] = 'Back to Course';