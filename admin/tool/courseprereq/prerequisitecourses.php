<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Prerequisites courses display page
 *
 * @package    tool_courseprereq
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require('../../../config.php');
require_once($CFG->dirroot . '/course/lib.php');

use tool_courseprereq\tool_courseprereq_utils;

// Get parameters
$courseid = required_param('courseid', PARAM_INT);
$returnurl = optional_param('returnurl', '', PARAM_LOCALURL);

// Verify course exists
$course = get_course($courseid);

require_login();

// Set up page
$url = new moodle_url('/admin/tool/courseprereq/prerequisitecourses.php', ['courseid' => $courseid]);
if ($returnurl) {
    $url->param('returnurl', $returnurl);
}
$PAGE->set_url($url);
$PAGE->set_context(context_system::instance());
$PAGE->set_pagelayout('standard');
$PAGE->set_title(get_string('prerequisitecourses', 'tool_courseprereq'));
$PAGE->set_heading($course->fullname);

// Check if plugin is enabled
if (!tool_courseprereq_utils::is_enabled()) {
    throw new moodle_exception('Plugin not enabled');
}

// Get current user
global $USER;

// Get prerequisite course statuses
$prereq_statuses = tool_courseprereq_utils::get_prereq_course_statuses($courseid, $USER->id);
$displaymode = tool_courseprereq_utils::get_displaymode();

// Filter courses based on display mode
$courses_to_display = [];
foreach ($prereq_statuses as $status) {
    if ($displaymode === 'all' || ($displaymode === 'pending' && !$status->completed)) {
        $courses_to_display[] = $status;
    }
}

// Get course details for display
$course_details = [];
if (!empty($courses_to_display)) {
    global $DB;
    $courseids = array_column($courses_to_display, 'courseid');
    $courses = $DB->get_records_list('course', 'id', $courseids, '', 'id, fullname');

    foreach ($courses_to_display as $status) {
        if (isset($courses[$status->courseid])) {
            $course_details[] = (object)[
                'id' => $status->courseid,
                'fullname' => $courses[$status->courseid]->fullname,
                'completed' => $status->completed,
                'url' => course_get_url($status->courseid)->out(false)
            ];
        }
    }
}

// Get formatted block message
$blockmessage = tool_courseprereq_utils::get_formatted_blockmessage();

// Set up return URL
if (empty($returnurl)) {
    $returnurl = course_get_url($courseid)->out(false);
}

echo $OUTPUT->header();

// Display the page content
?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page title -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><?php echo get_string('prerequisitecourses', 'tool_courseprereq'); ?></h2>
                <a href="<?php echo $returnurl; ?>" class="btn btn-secondary">
                    <?php echo get_string('back_to_course', 'tool_courseprereq'); ?>
                </a>
            </div>

            <!-- Block message -->
            <div class="alert alert-warning mb-4">
                <?php echo $blockmessage; ?>
            </div>

            <!-- Prerequisites list -->
            <?php if (!empty($course_details)): ?>
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0"><?php echo get_string('prerequisitecourses', 'tool_courseprereq'); ?></h4>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <?php foreach ($course_details as $course_detail): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="mr-3">
                                            <?php if ($course_detail->completed): ?>
                                                <span class="badge badge-success">
                                                    <i class="fa fa-check"></i>
                                                    <?php echo get_string('course_completed', 'tool_courseprereq'); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge badge-danger">
                                                    <i class="fa fa-times"></i>
                                                    <?php echo get_string('course_not_completed', 'tool_courseprereq'); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <a href="<?php echo $course_detail->url; ?>" class="font-weight-bold text-decoration-none">
                                                <?php echo format_string($course_detail->fullname); ?>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <p><?php echo get_string('blockmessage_default', 'tool_courseprereq'); ?></p>
                </div>
            <?php endif; ?>

            <!-- Return button -->
            <div class="mt-4 text-center">
                <a href="<?php echo $returnurl; ?>" class="btn btn-primary btn-lg">
                    <?php echo get_string('back_to_course', 'tool_courseprereq'); ?>
                </a>
            </div>
        </div>
    </div>
</div>

<?php
echo $OUTPUT->footer();
